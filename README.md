# Discord 서버 데이터 수집기

이 프로그램은 Discord 서버의 텍스트 기록, 링크, 이미지, 영상, 파일 등을 수집하는 도구입니다.

## ⚠️ 주의사항

이 프로그램은 사용자 계정(일반 Discord 계정)을 사용하여 데이터를 수집합니다. 이는 Discord의 서비스 약관(ToS)에 위배될 수 있으며, 계정 제재의 위험이 있습니다. 교육적 목적으로만 사용하세요.

## 설치 방법

1. Node.js를 설치하세요 (https://nodejs.org/)
2. 이 저장소를 클론하거나 다운로드하세요
3. 프로젝트 폴더에서 다음 명령어를 실행하여 필요한 패키지를 설치하세요:
   ```
   npm install
   ```

## 설정 방법

1. `.env.example` 파일을 `.env`로 복사하세요
2. `.env` 파일을 열고 다음 정보를 입력하세요:
   - 방법 1: Discord 계정 정보 사용 (자동으로 토큰 획득)
     - `DISCORD_EMAIL`: Discord 계정 이메일
     - `DISCORD_PASSWORD`: Discord 계정 비밀번호
   - 방법 2: 직접 토큰 입력 (선택 사항)
     - `USER_TOKEN`: Discord 사용자 계정 토큰
   - 필수 정보:
     - `SERVER_ID`: 데이터를 수집할 서버 ID

### Discord 사용자 토큰 직접 얻는 방법 (선택 사항)

1. Discord 웹 클라이언트(https://discord.com/app)에 로그인하세요
2. F12를 눌러 개발자 도구를 엽니다
3. 네트워크 탭을 선택합니다
4. 페이지를 새로고침합니다
5. 아무 요청이나 선택하고 요청 헤더에서 'authorization' 값을 찾습니다
6. 이 값이 사용자 토큰입니다

### 2단계 인증 사용자

2단계 인증을 사용하는 계정의 경우, 프로그램 실행 중에 인증 코드를 입력하라는 메시지가 표시됩니다. 인증 앱에서 코드를 확인하여 입력하세요.

### 서버 ID 얻는 방법

1. Discord 설정에서 개발자 모드를 활성화하세요 (설정 > 고급 > 개발자 모드)
2. 서버 이름을 마우스 오른쪽 버튼으로 클릭하고 "ID 복사"를 선택하세요

## 사용 방법

### 기본 사용법 (모든 채널 수집)

프로젝트 폴더에서 다음 명령어를 실행하세요:

```
node index.js
```

### 특정 채널만 수집

특정 채널만 수집하려면 `--channels` 또는 `-c` 옵션을 사용하고 채널 ID를 쉼표로 구분하여 지정하세요:

```
node index.js --channels 123456789012345678,876543210987654321
```

### 메시지 수 제한

채널당 수집할 메시지 수를 제한하려면 `--limit` 또는 `-l` 옵션을 사용하세요:

```
node index.js --limit 500
```

특정 채널에서 메시지 수를 제한하려면 다음과 같이 사용하세요:

```
node index.js --channels 123456789012345678 --limit 500
```

### 도움말 보기

사용 가능한 모든 옵션을 보려면 다음 명령어를 실행하세요:

```
node index.js --help
```

### 프로그램 중지 방법

프로그램을 중지하려면 터미널에서 `Ctrl+C`를 누르세요.

### 토큰 확인 방법

프로그램이 성공적으로 토큰을 획득하면 `discord_token.txt` 파일에 저장됩니다. 이 파일을 열어 토큰을 확인할 수 있습니다.

## 출력 결과

프로그램은 다음과 같은 구조로 데이터를 저장합니다:

```
(현재 프로그램 폴더)/
├── server_info.json       # 서버 정보
├── server_icon.png        # 서버 아이콘
├── channels.json          # 채널 목록
├── [채널명1]/             # 각 채널별 폴더
│   ├── messages.json      # 메시지 데이터 (JSON 형식)
│   ├── messages.txt       # 메시지 텍스트 (읽기 쉬운 형식)
│   ├── links/             # 추출된 링크 폴더
│   │   ├── all_urls.txt           # 모든 URL 목록
│   │   ├── mega_urls.txt          # 메가 URL 목록
│   │   ├── other_urls.txt         # 기타 URL 목록
│   │   ├── extracted_urls.json    # URL 정보 (JSON 형식)
│   │   └── mega_downloads/        # 다운로드된 메가 파일
│   │       ├── [파일들]           # 메가에서 다운로드한 파일
├── [채널명2]/
│   ├── ...
├── media/                 # 미디어 파일 폴더
│   ├── [채널명1]/         # 채널별 미디어 폴더
│   │   ├── [파일들]       # 다운로드된 미디어 파일
│   ├── [채널명2]/
│   │   ├── ...
```

## 주요 기능

- **Discord 채널 데이터 수집**: 서버의 모든 채널 또는 특정 채널의 메시지를 수집합니다.
- **첨부 파일 다운로드**: 메시지에 첨부된 이미지, 비디오, 문서 등을 자동으로 다운로드합니다.
- **메가(MEGA) 링크 처리**: 메시지에 포함된 메가 링크를 감지하고 파일을 자동으로 다운로드합니다.
- **URL 추출**: 메시지에 포함된 모든 URL을 추출하여 별도의 파일로 저장합니다.
- **ZIP 및 기타 파일 지원**: 모든 유형의 첨부 파일을 다운로드할 수 있습니다.

## 커스터마이징

- 명령줄 옵션을 사용하여 채널당 수집할 메시지 수를 조정할 수 있습니다: `--limit 500`
- 명령줄 옵션을 사용하여 특정 채널만 수집할 수 있습니다: `--channels 채널ID1,채널ID2`
- `scraper.js` 파일을 수정하여 추가 기능을 구현할 수 있습니다.
