const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const { URL } = require('url');
const { File, Storage } = require('megajs');
const fileType = require('file-type');

async function ensureDir(dir) {
  await fs.ensureDir(dir);
}

function sanitizeFilename(filename) {
  return filename.replace(/[\\/:*?"<>|]/g, '_');
}
async function downloadFile(url, outputPath) {
  try {
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream(outputPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Download failed: ${url}`, error.message);
  }
}

function getFilenameFromUrl(url) {
  try {
    const parsedUrl = new URL(url);
    const pathname = parsedUrl.pathname;
    let filename = path.basename(pathname);

    if (!filename || filename === '' || !path.extname(filename)) {
      const timestamp = Date.now();
      const ext = guessExtensionFromUrl(url) || '.bin';
      filename = `file_${timestamp}${ext}`;
    }

    return sanitizeFilename(filename);
  } catch (error) {
    const timestamp = Date.now();
    return `file_${timestamp}.bin`;
  }
}

function guessExtensionFromUrl(url) {
  if (url.includes('image')) return '.jpg';
  if (url.includes('video')) return '.mp4';
  if (url.includes('audio')) return '.mp3';
  return '';
}

async function saveTextFile(content, outputPath) {
  await fs.writeFile(outputPath, content, 'utf8');
}

async function saveJsonFile(data, outputPath) {
  await fs.writeFile(outputPath, JSON.stringify(data, null, 2), 'utf8');
}

/**
 * 텍스트에서 URL 추출
 * @param {string} text 텍스트
 * @returns {Array<string>} URL 배열
 */
function extractUrls(text) {
  if (!text) return [];

  // URL 정규식 패턴
  const urlRegex = /(https?:\/\/[^\s]+)/g;

  // 텍스트에서 URL 추출
  const matches = text.match(urlRegex);

  return matches || [];
}

/**
 * URL이 메가 링크인지 확인
 * @param {string} url URL
 * @returns {boolean} 메가 링크 여부
 */
function isMegaUrl(url) {
  return url.includes('mega.nz') || url.includes('mega.co.nz');
}

/**
 * 메가 링크에서 파일 다운로드
 * @param {string} url 메가 링크
 * @param {string} outputPath 저장 경로
 */
async function downloadMegaFile(url, outputPath) {
  try {
    console.log(`메가 파일 다운로드 시작: ${url}`);

    // 메가 파일 객체 생성
    const file = File.fromURL(url);

    // 파일 정보 가져오기
    await file.loadAttributes();

    // 파일 다운로드
    const fileStream = file.download();
    const writeStream = fs.createWriteStream(outputPath);

    fileStream.pipe(writeStream);

    return new Promise((resolve, reject) => {
      writeStream.on('finish', () => {
        console.log(`메가 파일 다운로드 완료: ${outputPath}`);
        resolve();
      });
      writeStream.on('error', (err) => {
        console.error(`메가 파일 다운로드 실패: ${url}`, err.message);
        reject(err);
      });
    });
  } catch (error) {
    console.error(`메가 파일 다운로드 실패: ${url}`, error.message);
  }
}

/**
 * 메시지에서 URL 추출 및 다운로드
 * @param {Array} messages 메시지 배열
 * @param {string} outputDir 출력 디렉토리
 */
async function processMessageUrls(messages, outputDir) {
  // 링크 저장 디렉토리 생성
  const linksDir = path.join(outputDir, 'links');
  await ensureDir(linksDir);

  // 추출된 모든 URL 저장
  const allUrls = new Set();
  const megaUrls = new Set();
  const otherUrls = new Set();

  // 메시지에서 URL 추출
  for (const message of messages) {
    if (message.content) {
      const urls = extractUrls(message.content);

      for (const url of urls) {
        allUrls.add(url);

        if (isMegaUrl(url)) {
          megaUrls.add(url);
        } else {
          otherUrls.add(url);
        }
      }
    }
  }

  // URL 목록 저장
  await saveJsonFile({
    all: Array.from(allUrls),
    mega: Array.from(megaUrls),
    other: Array.from(otherUrls)
  }, path.join(linksDir, 'extracted_urls.json'));

  await saveTextFile(Array.from(allUrls).join('\n'), path.join(linksDir, 'all_urls.txt'));
  await saveTextFile(Array.from(megaUrls).join('\n'), path.join(linksDir, 'mega_urls.txt'));
  await saveTextFile(Array.from(otherUrls).join('\n'), path.join(linksDir, 'other_urls.txt'));

  console.log(`총 ${allUrls.size}개 URL 추출됨 (메가: ${megaUrls.size}개, 기타: ${otherUrls.size}개)`);

  // 메가 파일 다운로드
  if (megaUrls.size > 0) {
    const megaDir = path.join(linksDir, 'mega_downloads');
    await ensureDir(megaDir);

    console.log(`메가 파일 다운로드 시작 (${megaUrls.size}개)...`);

    const downloadPromises = [];

    for (const url of megaUrls) {
      try {
        // 메가 링크에서 파일명 추출 시도
        const file = File.fromURL(url);
        await file.loadAttributes();

        const filename = sanitizeFilename(file.name) || `mega_file_${Date.now()}`;
        const outputPath = path.join(megaDir, filename);

        downloadPromises.push(downloadMegaFile(url, outputPath));
      } catch (error) {
        console.error(`메가 파일 정보 가져오기 실패: ${url}`, error.message);

        // 파일명을 가져올 수 없는 경우 타임스탬프 사용
        const timestamp = Date.now();
        const outputPath = path.join(megaDir, `mega_file_${timestamp}`);

        downloadPromises.push(downloadMegaFile(url, outputPath));
      }
    }

    // 모든 다운로드 작업 완료 대기
    await Promise.allSettled(downloadPromises);
    console.log('메가 파일 다운로드 완료');
  }

  return {
    total: allUrls.size,
    mega: megaUrls.size,
    other: otherUrls.size
  };
}

module.exports = {
  ensureDir,
  sanitizeFilename,
  downloadFile,
  getFilenameFromUrl,
  saveTextFile,
  saveJsonFile,
  extractUrls,
  isMegaUrl,
  downloadMegaFile,
  processMessageUrls
};
