# Contributing to Discord Server Scraper

Thank you for your interest in contributing to Discord Server Scraper! We welcome contributions from the community.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- Be respectful and inclusive
- Use welcoming and inclusive language
- Be collaborative
- Focus on what is best for the community
- Show empathy towards other community members

## How to Contribute

### Reporting Bugs

Before creating bug reports, please check the existing issues to avoid duplicates. When creating a bug report, include:

- A clear and descriptive title
- Steps to reproduce the issue
- Expected behavior vs actual behavior
- Your environment (OS, Node.js version, etc.)
- Any relevant logs or error messages

### Suggesting Enhancements

Enhancement suggestions are welcome! Please provide:

- A clear and descriptive title
- A detailed description of the proposed feature
- Explain why this enhancement would be useful
- Consider the scope and complexity of the change

### Pull Requests

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests if applicable
5. Ensure your code follows the existing style
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### Development Setup

1. Fork and clone the repository
2. Install dependencies: `npm install`
3. Copy `.env.example` to `.env` and configure
4. Make your changes
5. Test your changes thoroughly

## Guidelines

### Code Style

- Use consistent indentation (2 spaces)
- Use meaningful variable and function names
- Add comments for complex logic
- Follow existing code patterns

### Security

- Never commit sensitive information (tokens, passwords, etc.)
- Be mindful of security implications in your changes
- Follow secure coding practices

### Documentation

- Update README.md if your changes affect usage
- Add JSDoc comments for new functions
- Update examples if necessary

## Legal Considerations

By contributing to this project, you acknowledge that:

- Your contributions will be licensed under the MIT License
- You have the right to submit your contributions
- You understand the educational nature of this project
- You agree to the disclaimer regarding Discord's Terms of Service

## Questions?

If you have questions about contributing, please:

1. Check the existing documentation
2. Search through existing issues
3. Create a new issue with the "question" label

Thank you for contributing!
