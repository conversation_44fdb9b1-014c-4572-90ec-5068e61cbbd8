# ===========================================
# Discord Server Scraper Configuration
# ===========================================

# Discord Account Information
# Method 1: Use email and password (recommended for beginners)
DISCORD_EMAIL=<EMAIL>
DISCORD_PASSWORD=your_discord_password

# Method 2: Use Discord user token directly (advanced users)
# If you have a Discord user token, you can use it instead of email/password
# USER_TOKEN=your_discord_user_token_here

# Target Server Configuration
# The Discord server ID you want to scrape data from
# To get server ID: Enable Developer Mode in Discord settings, right-click server name, copy ID
SERVER_ID=123456789012345678

# Output Configuration
# Directory where scraped data will be saved (default: current directory)
# OUTPUT_DIR=./output

# ===========================================
# Security Notice
# ===========================================
# NEVER share your Discord credentials or token with anyone!
# NEVER commit the .env file to version control!
# This file should remain private and local to your machine.
